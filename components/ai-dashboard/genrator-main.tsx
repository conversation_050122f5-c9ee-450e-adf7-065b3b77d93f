"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Send } from "lucide-react";

// 导入响应式样式hooks
import { useCardStyles } from "./hooks/use-responsive-styles";
import { useDeviceLayout } from "./hooks/use-device-layout";

// 导入拆分的组件
import {
  ModelSelector,
  OptionsConfig,
  CostEstimate,
  CreditsDisplay,
  GenerateButton,
  PromptInput,
  useAIGeneration,
  type AINonFullMainProps
} from "./components";

export function GeneratorMain({
  modelType,
  onResultChange,
  onGeneratingChange,
  isFullscreen  // 新增：接收全屏状态参数
}: AINonFullMainProps & { isFullscreen?: boolean } = {}) {
  // 使用自定义hook管理所有状态和逻辑
  const {
    selectedModel,
    models,
    modelsLoading,
    modelsError,
    prompt,
    setPrompt,
    options,
    setOptions,
    loading,
    costEstimate,
    userCredits,
    handleGenerate,
    handleModelSelect
  } = useAIGeneration(modelType, onResultChange, onGeneratingChange);

  // 使用响应式样式
  const { isMobile } = useDeviceLayout();
  const { className: cardClassName } = useCardStyles();

  return (
    <div className={`w-full max-w-full ${
      isFullscreen ? 'h-full overflow-x-hidden' : ''  // 修复：只在全屏模式下设置overflow-x-hidden
    } ${isMobile ? 'space-y-1' : 'space-y-6'}`}>  {/* 优化：移动端最小垂直间距 */}
      {/* 统一的输入配置卡片 - 使用响应式样式 */}
      <Card className={`${cardClassName} bg-gradient-to-br from-card via-card to-accent/5 w-full max-w-full ${
        isFullscreen ? 'overflow-x-hidden overflow-y-hidden gap-0' : ''  // 修复：只在全屏模式下设置overflow
      }`}>
        <CardHeader className={`border-b border-border/30 bg-gradient-to-r from-muted/20 to-muted/10 ${
          isMobile ? 'pb-1 px-2 pt-2' : 'pb-4 px-6 pt-6'  // 优化：移动端最小header内边距
        }`}>
          <CardTitle className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>  {/* 优化：移动端减少间距 */}
            <div className={`rounded-lg bg-gradient-to-r from-primary to-accent ${
              isMobile ? 'p-1.5' : 'p-2'  // 优化：移动端减少图标容器大小
            }`}>
              <Send className={`text-primary-foreground ${
                isMobile ? 'w-4 h-4' : 'w-5 h-5'  // 优化：移动端减少图标大小
              }`} />
            </div>
            <span className={`bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent ${
              isMobile ? 'text-sm font-medium' : 'text-base font-semibold'  // 优化：移动端减少字体大小
            }`}>
              {isMobile ? 'AI CONFIG' : 'AI GENERATION CONFIG'}  {/* 优化：移动端简化标题 */}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className={`w-full max-w-full ${
          isFullscreen ? 'overflow-x-hidden' : ''  // 修复：只在全屏模式下设置overflow-x-hidden
        } ${isMobile ? 'space-y-1 px-2 py-1' : 'space-y-4 px-6 py-4'}`}>
          {/* 模型选择组件 */}
          <ModelSelector
            selectedModel={selectedModel}
            models={models}
            modelsLoading={modelsLoading}
            modelsError={modelsError}
            onModelSelect={handleModelSelect}
          />

          {/* 提示词输入组件 */}
          <PromptInput
            prompt={prompt}
            onPromptChange={setPrompt}
          />

          {/* 选项配置组件 */}
          <OptionsConfig
            selectedModel={selectedModel}
            options={options}
            onOptionsChange={setOptions}
          />

          {/* 成本预估组件 */}
          <CostEstimate costEstimate={costEstimate} />

          {/* 积分显示组件 */}
          <CreditsDisplay userCredits={userCredits} />

          {/* 生成按钮组件 */}
          <GenerateButton
            loading={loading}
            selectedModel={selectedModel}
            prompt={prompt}
            costEstimate={costEstimate}
            onGenerate={handleGenerate}
          />
        </CardContent>
      </Card>
    </div>
  );
}
