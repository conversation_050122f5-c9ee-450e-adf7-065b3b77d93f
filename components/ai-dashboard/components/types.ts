import { MessageSquare, Image, Video, Zap } from "lucide-react";

export interface AIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: string;
  provider: string;
  credits_per_unit: number;
  unit_type: string;
  description?: string; // 保留兼容性
  description_i18n?: Record<string, string>; // 新的多语言描述字段
  model_name_i18n?: Record<string, string>; // 新的多语言名称字段
  supported_features?: string[];
  icon?: string;
}

export interface GenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export interface AINonFullMainProps {
  modelType?: string;
  onResultChange?: (result: GenerationResult | null) => void;
  onGeneratingChange?: (isGenerating: boolean) => void;
}

export interface GenerationOptions {
  size?: string;
  aspectRatio?: string;
  variants?: number;
  temperature?: number;
  max_tokens?: number;
  cdn?: string;
  uploadedImages?: string[];
  referenceImages?: string[];
  firstFrameUrl?: string;

  // Replicate 特定选项
  output_quality?: number;
  output_format?: string;
  guidance?: number;
  num_inference_steps?: number;
  prompt_strength?: number;
  disable_safety_checker?: boolean;
  go_fast?: boolean;
  megapixels?: string;
  seed?: number;
  image?: string; // Input image for img2img mode
}

export interface CostEstimate {
  cost_estimate: {
    estimated_credits: number;
  };
  user_credits: {
    can_afford: boolean;
    shortfall?: number;
  };
}

export const MODEL_TYPE_ICONS = {
  text: MessageSquare,
  image: Image,
  video: Video,
  multimodal: Zap
};

export interface WorkspaceGenerationResult {
  id: string;
  type: string;
  status: string;
  progress?: number;
  result?: {
    text?: string;
    images?: Array<{ url: string; width: number; height: number }>;
    video?: { url: string };
  };
  error?: {
    reason: string;
    detail: string;
  };
  usage?: {
    credits_consumed: number;
  };
}

export interface ModelTypeConfig {
  value: string;
  label: string;
  icon: any;
  color: string;
}


