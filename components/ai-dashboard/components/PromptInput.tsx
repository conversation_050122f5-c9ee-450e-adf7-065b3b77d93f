"use client";

import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../hooks/use-device-layout";

interface PromptInputProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
}

export function PromptInput({ prompt, onPromptChange }: PromptInputProps) {
  const t = useTranslations("ai-dashboard.generator");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取超小屏幕状态

  return (
    <div className={`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full ${
      isSmallMobile ? 'px-1.5 py-1' :  // 优化：超小屏幕最小内边距
      isMobile ? 'px-2 py-1.5' : 'px-3 py-2'
    }`}>  {/* 修复：移除overflow-x-hidden避免滚动条问题 */}
      <Label htmlFor="prompt" className={`font-medium text-foreground ${
        isSmallMobile ? 'text-xs' :  // 优化：超小屏幕最小字体
        isMobile ? 'text-xs' : 'text-sm'
      }`}>{t("prompt_input")}</Label>
      <Textarea
        id="prompt"
        placeholder={t("prompt_placeholder")}
        value={prompt}
        onChange={(e) => onPromptChange(e.target.value)}
        rows={isSmallMobile ? 2 : isMobile ? 3 : 4}  // 优化：超小屏幕最少行数
        className={`bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border resize-none rounded-xl w-full max-w-full ${
          isSmallMobile ? 'mt-0.5 text-xs' :  // 优化：超小屏幕最小间距和字体
          isMobile ? 'mt-1 text-sm' : 'mt-2 text-base'
        }`}
      />
    </div>
  );
}
