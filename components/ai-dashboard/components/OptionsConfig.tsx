"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  RichSelect,
  RichSelectContent,
  RichSelectItem,
  RichSelectTrigger,
  RichSelectValue,
} from "@/components/ui/rich-select";
import { Upload, X, HelpCircle } from "lucide-react";
import { useState, useRef, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../hooks/use-device-layout";  // 优化：导入设备布局hook
import type { AIModel, GenerationOptions } from "./types";

interface OptionsConfigProps {
  selectedModel: AIModel | null;
  options: GenerationOptions;
  onOptionsChange: (options: GenerationOptions) => void;
}

export function OptionsConfig({
  selectedModel,
  options,
  onOptionsChange
}: OptionsConfigProps) {
  const t = useTranslations("ai-dashboard");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取设备状态
  const [uploadedImages, setUploadedImages] = useState<string[]>(options.uploadedImages || []);
  const [uploading, setUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // 同步外部 options 变化到本地状态
  useEffect(() => {
    setUploadedImages(options.uploadedImages || []);
  }, [options.uploadedImages]);

  // 防止页面默认的拖放行为
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault();
    };

    const handlePageDrop = (e: DragEvent) => {
      e.preventDefault();
      // 如果不是在我们的拖放区域内，则阻止默认行为
      if (!dropZoneRef.current?.contains(e.target as Node)) {
        return false;
      }
    };

    document.addEventListener('dragover', preventDefault);
    document.addEventListener('drop', handlePageDrop);

    return () => {
      document.removeEventListener('dragover', preventDefault);
      document.removeEventListener('drop', handlePageDrop);
    };
  }, []);

  if (!selectedModel) return null;

  const { model_type, supported_features = [], provider } = selectedModel;
  const isReplicateModel = provider === 'replicate';

  const updateOptions = (newOptions: Partial<GenerationOptions>) => {
    onOptionsChange({ ...options, ...newOptions });
  };

  // 处理文件上传的通用函数
  const processFileUpload = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert(t("errors.invalid_input"));
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/ai/upload-image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.code === 0) {
        const newImages = [...uploadedImages, result.data.url];
        setUploadedImages(newImages);
        updateOptions({ uploadedImages: newImages });
      } else {
        alert(result.msg || t("errors.network_error"));
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert(t("errors.network_error"));
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await processFileUpload(file);
  };

  // 拖放事件处理函数
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev + 1);

    // 检查是否包含文件
    if (e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => {
      const newCount = prev - 1;
      if (newCount === 0) {
        setIsDragOver(false);
      }
      return newCount;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 设置拖放效果
    if (e.dataTransfer.types.includes('Files')) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragOver(false);
    setDragCounter(0);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    // 过滤出图片文件
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert(t("errors.invalid_input"));
      return;
    }

    // 如果有多个图片文件，提示用户只会处理第一个
    if (imageFiles.length > 1) {
      alert(`检测到 ${imageFiles.length} 个图片文件，将上传第一个：${imageFiles[0].name}`);
    }

    // 处理第一个图片文件
    await processFileUpload(imageFiles[0]);
  };

  const removeImage = (index: number) => {
    const newImages = uploadedImages.filter((_, i) => i !== index);
    setUploadedImages(newImages);
    updateOptions({ uploadedImages: newImages });
  };

  const supportsImageUpload = supported_features.includes('image_upload');

  return (
    <div className={`w-full max-w-full overflow-x-hidden ${
      isSmallMobile ? 'space-y-1' : isMobile ? 'space-y-1.5' : 'space-y-2'  // 优化：响应式垂直间距
    }`}>
      {/* 图片上传功能 - 支持的模型显示 */}
      {supportsImageUpload && (
        <div className={`bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden ${
          isSmallMobile ? 'px-2 py-1.5' :  // 优化：超小屏幕减少内边距
          isMobile ? 'px-2.5 py-1.5' : 'px-3 py-2'
        }`}>
          <Label className={`font-medium text-foreground block ${
            isSmallMobile ? 'text-xs mb-1' :  // 优化：超小屏幕减少字体和间距
            isMobile ? 'text-xs mb-1.5' : 'text-sm mb-2'
          }`}>
            {model_type === 'video' ? t("options.first_frame") :
             model_type === 'image' ? t("options.reference_image") : t("options.image_upload")}
          </Label>

          <div className="space-y-2">
            {/* 拖放上传区域 */}
            <div
              ref={dropZoneRef}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              className={`
                relative border-2 border-dashed rounded-xl p-6 transition-all duration-300 cursor-pointer
                ${isDragOver
                  ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg shadow-primary/20'
                  : 'border-border/50 hover:border-border hover:bg-muted/20'
                }
                ${uploading ? 'pointer-events-none opacity-50' : ''}
                group
              `}
              onClick={() => !uploading && fileInputRef.current?.click()}
            >
              <div className="flex flex-col items-center justify-center gap-3 text-center">
                <div className={`
                  p-3 rounded-full transition-all duration-300
                  ${isDragOver
                    ? 'bg-primary text-primary-foreground scale-110 animate-pulse'
                    : 'bg-muted group-hover:bg-muted/80'
                  }
                `}>
                  <Upload className={`w-6 h-6 transition-transform duration-300 ${
                    isDragOver ? 'scale-110' : 'group-hover:scale-105'
                  }`} />
                </div>

                <div className="space-y-1">
                  <p className={`text-sm font-medium transition-colors duration-300 ${
                    isDragOver ? 'text-primary' : 'text-foreground'
                  }`}>
                    {uploading ? t("options.uploading") :
                     isDragOver ? t("options.drop_to_upload") : t("options.drag_drop")}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {t("options.supported_formats")}
                  </p>
                  {isDragOver && (
                    <p className="text-xs text-primary font-medium animate-bounce">
                      {t("options.file_detected")}
                    </p>
                  )}
                </div>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>

            {/* 已上传图片预览 */}
            {uploadedImages.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {uploadedImages.map((imageUrl, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={imageUrl}
                      alt={`${t("options.image_upload")} ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg border border-border/50"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeImage(index)}
                      className="absolute top-1 right-1 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 文本生成选项 */}
      {model_type === 'text' && (
        <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-full">
            <div>
              <Label htmlFor="max_tokens" className="text-sm font-medium text-foreground">{t("options.max_tokens")}</Label>
              <Input
                id="max_tokens"
                type="number"
                placeholder="1000"
                value={options.max_tokens || ''}
                onChange={(e) => updateOptions({ max_tokens: parseInt(e.target.value) || 1000 })}
                className="mt-1 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border rounded-xl w-full max-w-full"
              />
            </div>
            <div>
              <Label htmlFor="temperature" className="text-sm font-medium text-foreground">{t("options.temperature")}</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                min="0"
                max="1"
                placeholder="0.7"
                value={options.temperature || ''}
                onChange={(e) => updateOptions({ temperature: parseFloat(e.target.value) || 0.7 })}
                className="mt-1 bg-gradient-to-r from-background to-muted/30 border-border/50 focus:border-border rounded-xl w-full max-w-full"
              />
            </div>
          </div>
        </div>
      )}

      {/* 图像生成选项 */}
      {model_type === 'image' && (
        <div className="px-3 py-2 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 w-full max-w-full overflow-x-hidden">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-full">
            {supported_features.includes('variants') && (
              <div>
                <Label htmlFor="variants" className="text-sm font-medium text-foreground">{t("options.variants")}</Label>
                <RichSelect
                  value={options.variants?.toString() || '1'}
                  onValueChange={(value) => updateOptions({ variants: parseInt(value) })}
                >
                  <RichSelectTrigger size="lg" className="mt-1 w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl">
                    <RichSelectValue />
                  </RichSelectTrigger>
                  <RichSelectContent className="z-[150]">
                    <RichSelectItem value="1" option={{value: "1", label: t("options.1_image"), description: t("options.generate_1")}}>{t("options.1_image")}</RichSelectItem>
                    <RichSelectItem value="2" option={{value: "2", label: t("options.2_images"), description: t("options.generate_2")}}>{t("options.2_images")}</RichSelectItem>
                  </RichSelectContent>
                </RichSelect>
              </div>
            )}

            {(supported_features.includes('aspectRatio') || supported_features.includes('size')) && (
              <div>
                <Label htmlFor="size" className="text-sm font-medium text-foreground">{t("options.image_size")}</Label>
                <RichSelect
                  value={options.size || options.aspectRatio || '1:1'}
                  onValueChange={(value) => updateOptions({ size: value, aspectRatio: value })}
                >
                  <RichSelectTrigger size="lg" className="mt-1 w-full max-w-full bg-gradient-to-r from-background to-muted/30 border-border/50 hover:border-border rounded-xl">
                    <RichSelectValue />
                  </RichSelectTrigger>
                  <RichSelectContent className="z-[150]">
                    <RichSelectItem value="1:1" option={{value: "1:1", label: t("options.square"), description: "1:1"}}>{t("options.square_ratio")}</RichSelectItem>
                    <RichSelectItem value="16:9" option={{value: "16:9", label: t("options.landscape"), description: "16:9"}}>{t("options.landscape_ratio")}</RichSelectItem>
                    <RichSelectItem value="9:16" option={{value: "9:16", label: t("options.portrait"), description: "9:16"}}>{t("options.portrait_ratio")}</RichSelectItem>
                    <RichSelectItem value="4:3" option={{value: "4:3", label: "Standard", description: "4:3"}}>Standard (4:3)</RichSelectItem>
                    <RichSelectItem value="3:2" option={{value: "3:2", label: "Photo", description: "3:2"}}>Photo (3:2)</RichSelectItem>
                  </RichSelectContent>
                </RichSelect>
              </div>
            )}
          </div>

          {/* Replicate 特定选项 */}
          {isReplicateModel && (
            <div className="mt-4 pt-4 border-t border-border/30">
              <h4 className="text-sm font-semibold text-foreground mb-3">Replicate Advanced Options</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {/* Aspect Ratio */}
                <div>
                  <Label htmlFor="aspect_ratio" className="text-sm font-medium text-foreground">Aspect Ratio</Label>
                  <RichSelect
                    value={options.aspectRatio || '1:1'}
                    onValueChange={(value) => updateOptions({ aspectRatio: value })}
                  >
                    <RichSelectTrigger size="lg" className="mt-1 w-full">
                      <RichSelectValue />
                    </RichSelectTrigger>
                    <RichSelectContent className="z-[150]">
                      <RichSelectItem value="1:1" option={{value: "1:1", label: "1:1 Square", description: "Perfect for social media posts"}}>1:1 Square</RichSelectItem>
                      <RichSelectItem value="16:9" option={{value: "16:9", label: "16:9 Landscape", description: "Widescreen format"}}>16:9 Landscape</RichSelectItem>
                      <RichSelectItem value="21:9" option={{value: "21:9", label: "21:9 Ultra Wide", description: "Cinematic format"}}>21:9 Ultra Wide</RichSelectItem>
                      <RichSelectItem value="3:2" option={{value: "3:2", label: "3:2 Photo", description: "Classic photo ratio"}}>3:2 Photo</RichSelectItem>
                      <RichSelectItem value="2:3" option={{value: "2:3", label: "2:3 Portrait Photo", description: "Vertical photo ratio"}}>2:3 Portrait Photo</RichSelectItem>
                      <RichSelectItem value="4:5" option={{value: "4:5", label: "4:5 Instagram", description: "Instagram portrait"}}>4:5 Instagram</RichSelectItem>
                      <RichSelectItem value="5:4" option={{value: "5:4", label: "5:4 Classic", description: "Classic landscape"}}>5:4 Classic</RichSelectItem>
                      <RichSelectItem value="3:4" option={{value: "3:4", label: "3:4 Portrait", description: "Standard portrait"}}>3:4 Portrait</RichSelectItem>
                      <RichSelectItem value="4:3" option={{value: "4:3", label: "4:3 Standard", description: "Standard landscape"}}>4:3 Standard</RichSelectItem>
                      <RichSelectItem value="9:16" option={{value: "9:16", label: "9:16 Mobile", description: "Mobile portrait"}}>9:16 Mobile</RichSelectItem>
                      <RichSelectItem value="9:21" option={{value: "9:21", label: "9:21 Ultra Tall", description: "Ultra tall portrait"}}>9:21 Ultra Tall</RichSelectItem>
                    </RichSelectContent>
                  </RichSelect>
                </div>

                {/* Number of Outputs */}
                <div>
                  <Label htmlFor="num_outputs" className="text-sm font-medium text-foreground">Number of Outputs</Label>
                  <RichSelect
                    value={(options.variants || 1).toString()}
                    onValueChange={(value) => updateOptions({ variants: parseInt(value) })}
                  >
                    <RichSelectTrigger size="lg" className="mt-1 w-full">
                      <RichSelectValue />
                    </RichSelectTrigger>
                    <RichSelectContent className="z-[150]">
                      <RichSelectItem value="1" option={{value: "1", label: "1 Image", description: "Generate 1 image"}}>1 Image</RichSelectItem>
                      <RichSelectItem value="2" option={{value: "2", label: "2 Images", description: "Generate 2 images"}}>2 Images</RichSelectItem>
                      <RichSelectItem value="3" option={{value: "3", label: "3 Images", description: "Generate 3 images"}}>3 Images</RichSelectItem>
                      <RichSelectItem value="4" option={{value: "4", label: "4 Images", description: "Generate 4 images"}}>4 Images</RichSelectItem>
                    </RichSelectContent>
                  </RichSelect>
                </div>

                {/* Output Quality */}
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="output_quality" className="text-sm font-medium text-foreground">Output Quality</Label>
                    <div
                      className="cursor-help"
                      title="Quality when saving the output images, from 0 to 100. 100 is best quality, 0 is lowest quality. Not relevant for .png outputs"
                    >
                      <HelpCircle className="w-3 h-3 text-muted-foreground" />
                    </div>
                  </div>
                  <Input
                    id="output_quality"
                    type="number"
                    min="0"
                    max="100"
                    value={options.output_quality || 80}
                    onChange={(e) => updateOptions({ output_quality: parseInt(e.target.value) })}
                    className="mt-1"
                  />
                </div>

                {/* Output Format */}
                <div>
                  <Label htmlFor="output_format" className="text-sm font-medium text-foreground">Output Format</Label>
                  <RichSelect
                    value={options.output_format || 'webp'}
                    onValueChange={(value) => updateOptions({ output_format: value })}
                  >
                    <RichSelectTrigger size="lg" className="mt-1 w-full">
                      <RichSelectValue />
                    </RichSelectTrigger>
                    <RichSelectContent className="z-[150]">
                      <RichSelectItem value="webp" option={{value: "webp", label: "WebP", description: "Modern format with high compression"}}>WebP</RichSelectItem>
                      <RichSelectItem value="jpg" option={{value: "jpg", label: "JPG", description: "Universal format, good compression"}}>JPG</RichSelectItem>
                      <RichSelectItem value="png" option={{value: "png", label: "PNG", description: "Lossless compression, larger files"}}>PNG</RichSelectItem>
                    </RichSelectContent>
                  </RichSelect>
                </div>

                {/* Guidance */}
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="guidance" className="text-sm font-medium text-foreground">Guidance</Label>
                    <div
                      className="cursor-help"
                      title="Guidance for generated image. Higher values follow the prompt more closely"
                    >
                      <HelpCircle className="w-3 h-3 text-muted-foreground" />
                    </div>
                  </div>
                  <Input
                    id="guidance"
                    type="number"
                    min="0"
                    max="10"
                    step="0.1"
                    value={options.guidance || 3}
                    onChange={(e) => updateOptions({ guidance: parseFloat(e.target.value) })}
                    className="mt-1"
                  />
                </div>

                {/* Inference Steps */}
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="num_inference_steps" className="text-sm font-medium text-foreground">Inference Steps</Label>
                    <div
                      className="cursor-help"
                      title="Number of denoising steps. Recommended range is 28-50. Lower steps = faster but lower quality"
                    >
                      <HelpCircle className="w-3 h-3 text-muted-foreground" />
                    </div>
                  </div>
                  <Input
                    id="num_inference_steps"
                    type="number"
                    min="1"
                    max="50"
                    value={options.num_inference_steps || 28}
                    onChange={(e) => updateOptions({ num_inference_steps: parseInt(e.target.value) })}
                    className="mt-1"
                  />
                </div>

                {/* Seed */}
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="seed" className="text-sm font-medium text-foreground">Seed</Label>
                    <div
                      className="cursor-help"
                      title="Random seed. Set for reproducible generation"
                    >
                      <HelpCircle className="w-3 h-3 text-muted-foreground" />
                    </div>
                  </div>
                  <Input
                    id="seed"
                    type="number"
                    value={options.seed || ''}
                    onChange={(e) => updateOptions({ seed: e.target.value ? parseInt(e.target.value) : undefined })}
                    placeholder="Random if empty"
                    className="mt-1"
                  />
                </div>

                {/* Prompt Strength */}
                <div>
                  <div className="flex items-center gap-1">
                    <Label htmlFor="prompt_strength" className="text-sm font-medium text-foreground">Prompt Strength</Label>
                    <div
                      className="cursor-help"
                      title="Prompt strength when using img2img. 1.0 corresponds to full destruction of information in image"
                    >
                      <HelpCircle className="w-3 h-3 text-muted-foreground" />
                    </div>
                  </div>
                  <Input
                    id="prompt_strength"
                    type="number"
                    min="0"
                    max="1"
                    step="0.1"
                    value={options.prompt_strength || 0.8}
                    onChange={(e) => updateOptions({ prompt_strength: parseFloat(e.target.value) })}
                    className="mt-1"
                  />
                </div>

                {/* Megapixels */}
                <div>
                  <Label htmlFor="megapixels" className="text-sm font-medium text-foreground">Image Size</Label>
                  <RichSelect
                    value={options.megapixels || '1'}
                    onValueChange={(value) => updateOptions({ megapixels: value })}
                  >
                    <RichSelectTrigger size="lg" className="mt-1 w-full">
                      <RichSelectValue />
                    </RichSelectTrigger>
                    <RichSelectContent className="z-[150]">
                      <RichSelectItem value="1" option={{value: "1", label: "1 Megapixel", description: "Standard quality, balanced speed"}}>1 Megapixel</RichSelectItem>
                      <RichSelectItem value="0.25" option={{value: "0.25", label: "0.25 Megapixel", description: "Faster generation, lower resolution"}}>0.25 Megapixel</RichSelectItem>
                    </RichSelectContent>
                  </RichSelect>
                </div>

                {/* Go Fast */}
                <div>
                  <Label htmlFor="go_fast" className="text-sm font-medium text-foreground">Go Fast</Label>
                  <RichSelect
                    value={options.go_fast !== false ? 'true' : 'false'}
                    onValueChange={(value) => updateOptions({ go_fast: value === 'true' })}
                  >
                    <RichSelectTrigger size="lg" className="mt-1 w-full">
                      <RichSelectValue />
                    </RichSelectTrigger>
                    <RichSelectContent className="z-[150]">
                      <RichSelectItem value="true" option={{value: "true", label: "Enabled", description: "Faster predictions with fp8 quantization"}}>Enabled (Faster)</RichSelectItem>
                      <RichSelectItem value="false" option={{value: "false", label: "Disabled", description: "Original bf16 precision, deterministic"}}>Disabled (Precise)</RichSelectItem>
                    </RichSelectContent>
                  </RichSelect>
                </div>

                {/* Disable Safety Checker */}
                <div>
                  <Label htmlFor="disable_safety_checker" className="text-sm font-medium text-foreground">Safety Checker</Label>
                  <RichSelect
                    value={options.disable_safety_checker ? 'disabled' : 'enabled'}
                    onValueChange={(value) => updateOptions({ disable_safety_checker: value === 'disabled' })}
                  >
                    <RichSelectTrigger size="lg" className="mt-1 w-full">
                      <RichSelectValue />
                    </RichSelectTrigger>
                    <RichSelectContent className="z-[150]">
                      <RichSelectItem value="enabled" option={{value: "enabled", label: "Enabled", description: "Safety checker is active"}}>Enabled</RichSelectItem>
                      <RichSelectItem value="disabled" option={{value: "disabled", label: "Disabled", description: "Disable safety checker for generated images"}}>Disabled</RichSelectItem>
                    </RichSelectContent>
                  </RichSelect>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
