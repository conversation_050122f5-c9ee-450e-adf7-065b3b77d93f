"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../hooks/use-device-layout";  // 优化：导入设备布局hook
import type { CostEstimate as CostEstimateType } from "./types";

interface CostEstimateProps {
  costEstimate: CostEstimateType | null;
}

export function CostEstimate({ costEstimate }: CostEstimateProps) {
  const t = useTranslations("ai-dashboard.cost");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取设备状态

  if (!costEstimate) return null;

  return (
    <Alert className={`bg-gradient-to-r from-accent/10 to-primary/10 border-border/30 backdrop-blur-sm w-full max-w-full ${
      isSmallMobile ? 'p-2' : isMobile ? 'p-3' : 'p-4'  // 优化：响应式内边距
    }`}>  {/* 修复：移除overflow-x-hidden避免滚动条问题 */}
      <AlertCircle className={`${isSmallMobile ? 'h-3 w-3' : 'h-4 w-4'}`} />  {/* 优化：响应式图标大小 */}
      <AlertDescription className={isSmallMobile ? 'text-xs' : 'text-sm'}>  {/* 优化：响应式字体大小 */}
        {t("estimated")}: {costEstimate.cost_estimate.estimated_credits} {t("credits")}
        {!costEstimate.user_credits.can_afford && (
          <span className={`text-destructive ${isSmallMobile ? 'ml-1' : 'ml-2'}`}>  {/* 优化：响应式间距 */}
            ({t("not_enough", { shortfall: costEstimate.user_credits.shortfall ?? 0 })})
          </span>
        )}
      </AlertDescription>
    </Alert>
  );
}
