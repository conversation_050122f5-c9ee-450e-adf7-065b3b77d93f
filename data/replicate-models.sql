-- Replicate 模型配置
-- 添加 flux-krea-dev 模型

INSERT INTO ai_models (
    model_id, 
    model_name, 
    model_type, 
    provider, 
    api_endpoint, 
    credits_per_unit, 
    unit_type, 
    is_active,
    description_i18n,
    model_name_i18n,
    supported_features,
    max_input_size
) VALUES (
    'black-forest-labs/flux-krea-dev',
    'Flux Krea Dev',
    'image',
    'replicate',
    '/replicate/image',
    25,
    'images',
    false, -- 设置为不活跃，避免在前端显示
    '{"en": "An opinionated text-to-image model from Black Forest Labs in collaboration with Krea that excels in photorealism. Creates images that avoid the oversaturated AI look.", "zh": "来自 Black Forest Labs 与 Krea 合作的专业文本转图像模型，擅长生成逼真的照片效果，避免过度饱和的 AI 风格。"}',
    '{"en": "Flux Krea Dev", "zh": "Flux Krea 开发版"}',
    '["text2image", "photorealism", "high_quality"]',
    2000
);

-- 验证插入结果
SELECT 
    model_id,
    model_name,
    model_type,
    provider,
    credits_per_unit,
    is_active
FROM ai_models 
WHERE provider = 'replicate';
