:root {
  --background: oklch(0.98 0.01 260);
  --foreground: oklch(0.15 0.02 260);
  --card: oklch(1.0 0 0);
  --card-foreground: oklch(0.15 0.02 260);
  --popover: oklch(1.0 0 0);
  --popover-foreground: oklch(0.15 0.02 260);
  --primary: oklch(0.65 0.15 260);
  --primary-foreground: oklch(1.0 0 0);
  --secondary: oklch(0.95 0.02 260);
  --secondary-foreground: oklch(0.25 0.02 260);
  --muted: oklch(0.96 0.01 260);
  --muted-foreground: oklch(0.45 0.02 260);
  --accent: oklch(0.75 0.12 200);
  --accent-foreground: oklch(0.15 0.02 260);
  --destructive: oklch(0.65 0.15 15);
  --destructive-foreground: oklch(1.0 0 0);
  --border: oklch(0.92 0.02 260);
  --input: oklch(0.98 0.01 260);
  --ring: oklch(0.65 0.15 260);
  --chart-1: oklch(0.7002 0.1597 350.7532);
  --chart-2: oklch(0.8189 0.0799 212.0892);
  --chart-3: oklch(0.9195 0.0801 87.6670);
  --chart-4: oklch(0.7998 0.1110 348.1791);
  --chart-5: oklch(0.6197 0.1899 353.9091);
  --sidebar: oklch(0.97 0.01 260);
  --sidebar-foreground: oklch(0.25 0.02 260);
  --sidebar-primary: oklch(0.65 0.15 260);
  --sidebar-primary-foreground: oklch(1.0 0 0);
  --sidebar-accent: oklch(0.75 0.12 200);
  --sidebar-accent-foreground: oklch(0.15 0.02 260);
  --sidebar-border: oklch(0.92 0.02 260);
  --sidebar-ring: oklch(0.65 0.15 260);
  --font-sans: Poppins, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.4rem;
  --shadow-2xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-sm: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* 现代化渐变变量 */
  --gradient-primary: linear-gradient(135deg, oklch(0.65 0.15 260) 0%, oklch(0.75 0.12 200) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.95 0.02 260) 0%, oklch(0.98 0.01 200) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.75 0.12 200) 0%, oklch(0.65 0.15 260) 100%);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.08 0.01 260);
  --foreground: oklch(0.98 0.005 260);
  --card: oklch(0.11 0.015 260);
  --card-foreground: oklch(0.98 0.005 260);
  --popover: oklch(0.11 0.015 260);
  --popover-foreground: oklch(0.98 0.005 260);
  --primary: oklch(0.75 0.12 200);
  --primary-foreground: oklch(0.08 0.01 260);
  --secondary: oklch(0.15 0.015 260);
  --secondary-foreground: oklch(0.90 0.005 260);
  --muted: oklch(0.13 0.015 260);
  --muted-foreground: oklch(0.70 0.01 260);
  --accent: oklch(0.65 0.15 220);
  --accent-foreground: oklch(0.98 0.005 260);
  --destructive: oklch(0.75 0.15 15);
  --destructive-foreground: oklch(0.08 0.01 260);
  --border: oklch(0.20 0.015 260);
  --input: oklch(0.13 0.015 260);
  --ring: oklch(0.75 0.12 200);
  --chart-1: oklch(0.6998 0.0896 201.8672);
  --chart-2: oklch(0.7794 0.0803 4.1330);
  --chart-3: oklch(0.6699 0.0988 356.9762);
  --chart-4: oklch(0.4408 0.0702 217.0848);
  --chart-5: oklch(0.2713 0.0086 255.5780);
  --sidebar: oklch(0.09 0.01 260);
  --sidebar-foreground: oklch(0.90 0.005 260);
  --sidebar-primary: oklch(0.75 0.12 200);
  --sidebar-primary-foreground: oklch(0.08 0.01 260);
  --sidebar-accent: oklch(0.65 0.15 220);
  --sidebar-accent-foreground: oklch(0.98 0.005 260);
  --sidebar-border: oklch(0.20 0.015 260);
  --sidebar-ring: oklch(0.75 0.12 200);
  --font-sans: Poppins, sans-serif;
  --font-serif: Lora, serif;
  --font-mono: Fira Code, monospace;
  --radius: 0.4rem;
  --shadow-2xs: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-xs: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-sm: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);

  /* 暗色主题渐变变量 */
  --gradient-primary: linear-gradient(135deg, oklch(0.75 0.12 200) 0%, oklch(0.65 0.15 260) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.15 0.015 260) 0%, oklch(0.13 0.015 200) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.65 0.15 260) 0%, oklch(0.75 0.12 200) 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}
