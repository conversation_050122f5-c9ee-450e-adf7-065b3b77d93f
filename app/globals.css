@import "tailwindcss";
@import "./theme.css";

@plugin "tailwindcss-animate";

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground antialiased;
  }

  body:not(.dark) {
    background: linear-gradient(135deg, oklch(0.98 0.01 260) 0%, oklch(0.96 0.02 200) 100%);
  }

  body.dark {
    background: linear-gradient(135deg, oklch(0.08 0.01 260) 0%, oklch(0.06 0.01 200) 100%);
  }

  /* 现代化滚动条 - 明色主题 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  body:not(.dark) ::-webkit-scrollbar-thumb {
    background: oklch(0.75 0.12 200);
    border-radius: 4px;
  }

  body:not(.dark) ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.65 0.15 260);
  }

  /* 暗色主题滚动条 */
  body.dark ::-webkit-scrollbar-thumb {
    background: oklch(0.30 0.02 260);
    border-radius: 4px;
  }

  body.dark ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.40 0.03 260);
  }
}

.container {
  @apply mx-auto max-w-7xl px-4 md:px-8;
}

/* 现代化动画类 */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .glass-effect {
    background: rgba(var(--background), 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(var(--border), 0.2);
  }

  .gradient-text {
    background: linear-gradient(135deg, oklch(0.65 0.15 260) 0%, oklch(0.75 0.12 200) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* AI模型模块全屏样式 - 现代化版本 */
div.ai-module-fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 40 !important;
  overflow: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  max-width: none !important;
  animation: fadeIn 0.3s ease-out;
}

/* 全屏模式背景 - 支持明暗主题 */
body:not(.dark) div.ai-module-fullscreen {
  background: linear-gradient(135deg, oklch(0.98 0.01 260) 0%, oklch(0.96 0.02 200) 100%) !important;
}

body.dark div.ai-module-fullscreen {
  background: linear-gradient(135deg, oklch(0.08 0.01 260) 0%, oklch(0.06 0.01 200) 100%) !important;
}

/* 确保全屏时移除所有可能的限制 */
div.ai-module-fullscreen * {
  max-width: none !important;
}

/* 全屏模式下的下拉框样式修复 */
div.ai-module-fullscreen [data-radix-popper-content-wrapper] {
  z-index: 10000 !important;
}

div.ai-module-fullscreen [data-slot="select-content"] {
  z-index: 10000 !important;
}

/* 现代化三栏布局样式 - 1:3:6 比例 */
.ai-dashboard-three-column {
  display: flex;
  height: 100%;
  min-height: 0;
  gap: 1.5rem;
}

.ai-dashboard-sidebar {
  flex: 0 0 16%; /* 优化比例 */
  background: linear-gradient(135deg, oklch(0.97 0.01 260) 0%, oklch(0.95 0.02 200) 100%);
  border: 1px solid oklch(0.92 0.02 260);
  border-radius: 1rem;
  overflow-y: auto;
  min-width: 200px;
  max-width: 280px;
  padding: 1rem;
  backdrop-filter: blur(10px);
}

.ai-dashboard-main {
  flex: 0 0 32%; /* 优化比例 */
  min-width: 0;
  overflow-y: auto; /* 恢复：这个设置可能被其他地方需要 */
  min-width: 320px;
}

.ai-dashboard-result {
  flex: 0 0 52%; /* 优化比例 */
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 400px;
}

.ai-dashboard-result-content {
  flex: 1;
  overflow-y: auto;
}

/* 简化的全屏模式样式 - 现在直接在组件上设置z-index */


