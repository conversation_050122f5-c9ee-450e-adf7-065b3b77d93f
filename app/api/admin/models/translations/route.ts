import { respData, respErr } from "@/lib/resp";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";

export const runtime = "edge";

export async function PUT(req: Request) {
  try {
    // 验证用户身份（这里应该添加管理员权限检查）
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // TODO: 添加管理员权限检查
    // const isAdmin = await checkAdminPermission(user_uuid);
    // if (!isAdmin) {
    //   return respErr("Admin permission required", -3);
    // }

    const { modelId, translations } = await req.json();

    if (!modelId || !translations) {
      return respErr("Missing required parameters: modelId, translations");
    }

    const supabase = getSupabaseClient();

    // 更新模型的多语言字段
    const { data, error } = await supabase
      .from("ai_models")
      .update({
        model_name_i18n: translations.name,
        description_i18n: translations.description,
        updated_at: new Date().toISOString(),
      })
      .eq("model_id", modelId)
      .select()
      .single();

    if (error) {
      console.error("Failed to update model translations:", error);
      return respErr("Failed to update translations");
    }

    return respData({
      model: data,
      message: "Translations updated successfully"
    });
  } catch (error) {
    console.error("Translation update error:", error);
    return respErr("Internal server error");
  }
}

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const modelId = url.searchParams.get('modelId');

    const supabase = getSupabaseClient();

    const baseQuery = supabase
      .from("ai_models")
      .select("model_id, model_name, model_name_i18n, description, description_i18n, model_type, provider");

    let data, error;

    if (modelId) {
      ({ data, error } = await baseQuery.eq("model_id", modelId).single());
    } else {
      ({ data, error } = await baseQuery.eq("is_active", true).order("model_type").order("model_name"));
    }

    if (error) {
      console.error("Failed to fetch model translations:", error);
      return respErr("Failed to fetch translations");
    }

    return respData({
      models: modelId ? [data] : data,
      total: modelId ? 1 : (data as any[]).length
    });
  } catch (error) {
    console.error("Translation fetch error:", error);
    return respErr("Internal server error");
  }
}
