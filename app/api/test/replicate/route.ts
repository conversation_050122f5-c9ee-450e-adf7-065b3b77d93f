import { respData, respErr } from "@/lib/resp";
import { replicateProvider } from "@/services/replicate-provider";

export const runtime = "edge";

export async function POST(req: Request) {
  try {
    const { prompt } = await req.json();
    
    if (!prompt) {
      return respErr("Prompt is required");
    }

    console.log(`[Test Replicate] Testing with prompt: ${prompt}`);

    // 测试 Replicate provider
    const response = await replicateProvider.generateImage({
      model: 'black-forest-labs/flux-krea-dev',
      prompt,
      options: {
        output_quality: 90,
        output_format: 'webp'
      }
    });

    console.log(`[Test Replicate] Response:`, response);

    return respData({
      success: true,
      response
    });

  } catch (error) {
    console.error(`[Test Replicate] Error:`, error);
    return respErr(error instanceof Error ? error.message : 'Unknown error');
  }
}
